package com.yutoudev.irrigation.irr.service.history;

import com.yutoudev.irrigation.irr.controller.admin.history.vo.rainfallday.*;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;

import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryRainfallDayDO;
import com.yutoudev.irrigation.irr.convert.history.HistoryRainfallDayConvert;
import com.yutoudev.irrigation.irr.dal.mysql.history.HistoryRainfallDayMapper;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;

/**
 *
 * 历史日降雨量Service实现类
 * @description 管理后台-历史日降雨量Service实现类
 * <AUTHOR>
 * @time 2024-12-01 17:56:08
 *
 */
@Service
@Validated
public class HistoryRainfallDayServiceImpl extends TalonsServiceImpl<HistoryRainfallDayMapper,HistoryRainfallDayDO> implements HistoryRainfallDayService<HistoryRainfallDayDO> {

    @Resource
    private HistoryRainfallDayMapper historyRainfallDayMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(HistoryRainfallDayDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(HistoryRainfallDayCreateReqVO createReqVO) {
        // 插入
        HistoryRainfallDayDO historyRainfallDay = HistoryRainfallDayConvert.INSTANCE.convert(createReqVO);
        this.save(historyRainfallDay, true);
        // 返回
        return historyRainfallDay.getId();
    }

    @Override
    public boolean createBatch(List<HistoryRainfallDayCreateReqVO> list) {

        List<HistoryRainfallDayDO> saveList = HistoryRainfallDayConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        }else{
            throw exception(HISTORY_RAINFALL_DAY_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(HistoryRainfallDayUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateHistoryRainfallDayExists(updateReqVO.getId());
        // 更新
        HistoryRainfallDayDO updateObj = HistoryRainfallDayConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<HistoryRainfallDayUpdateReqVO> list) {

        List<HistoryRainfallDayDO> updateList = HistoryRainfallDayConvert.INSTANCE.convertUpdateBatch(list);

        for(HistoryRainfallDayDO tempDO: updateList){
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateHistoryRainfallDayExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(HISTORY_RAINFALL_DAY_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateHistoryRainfallDayExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(HISTORY_RAINFALL_DAY_DELETE_BATCH_ERROR);
        }
    }

    private void validateHistoryRainfallDayExists(Long id) {
        if (historyRainfallDayMapper.selectById(id) == null) {
            throw exception(HISTORY_RAINFALL_DAY_NOT_EXISTS);
        }
    }

    @Override
    public HistoryRainfallDayDO get(Long id) {
        return this.getById(id, true);
    }
    @Override
    public List<HistoryRainfallDayDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<HistoryRainfallDayDO> page(HistoryRainfallDayPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<HistoryRainfallDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<HistoryRainfallDayDO>() { };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<HistoryRainfallDayDO> pageResult = historyRainfallDayMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
            talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<HistoryRainfallDayDO> getList(HistoryRainfallDayQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<HistoryRainfallDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<HistoryRainfallDayDO>() { };
        queryBuilder.build(queryReqVO);

        List<HistoryRainfallDayDO> result = historyRainfallDayMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<HistoryRainfallDayExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(HISTORY_RAINFALL_DAY_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<HistoryRainfallDayDO> saveList = HistoryRainfallDayConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            HistoryRainfallDayDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, HistoryRainfallDayExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}
