package com.yutoudev.irrigation.irr.controller.admin.measind;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday.*;
import com.yutoudev.irrigation.irr.convert.measind.MeasIndDayConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndDayDO;
import com.yutoudev.irrigation.irr.service.measind.MeasIndDayService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 *
 * 量测设备指标统计日
 * @description 管理后台-量测设备指标统计日controller
 * <AUTHOR>
 * @time 2024-06-16 20:28:51
 *
 */
@RestController
@RequestMapping("/irr/meas-ind-day")
@Validated
public class MeasIndDayController {

    private static final String MODULE_NAME = "量测设备指标统计日";

    @Resource
    private MeasIndDayService<MeasIndDayDO> measIndDayService;

    /**
     * 创建量测设备指标统计日
     * @description 单个对象保存
     * @param createReqVO MeasIndDayCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody MeasIndDayCreateReqVO createReqVO) {
        return success(measIndDayService.create(createReqVO));
    }

    /**
     * 批量创建量测设备指标统计日
     * @description 多个对象保存
     * @param lists  MeasIndDayCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<MeasIndDayCreateReqVO> lists) {
        return success(measIndDayService.createBatch(lists));
    }

    /**
     * 更新量测设备指标统计日
     * @description 单个对象修改
     * @param updateReqVO MeasIndDayUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody MeasIndDayUpdateReqVO updateReqVO) {
        measIndDayService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新量测设备指标统计日
     * @description 批量更新
     * @param lists 批量更新列表 MeasIndDayUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<MeasIndDayUpdateReqVO> lists) {
        return success(measIndDayService.updateBatch(lists));
    }

    /**
     * 删除量测设备指标统计日
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        measIndDayService.delete(id);
        return success(true);
    }

    /**
     * 批量删除量测设备指标统计日
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(measIndDayService.deleteBatch(ids));
    }

    /**
     * 获得量测设备指标统计日详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<MeasIndDayDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<MeasIndDayDetailRespVO> get(@RequestParam("id") Long id) {
        MeasIndDayDO measIndDay = measIndDayService.get(id);
        return success(MeasIndDayConvert.INSTANCE.convertDetail(measIndDay));
    }

    /**
     * 量测设备指标统计日列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 MeasIndDayQueryReqVO
     * @return CommonResult<List<MeasIndDayRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<MeasIndDayRespVO>> getList(@RequestQueryParam MeasIndDayQueryReqVO queryReqVO) {
        List<MeasIndDayDO> list = measIndDayService.getList(queryReqVO);
        return success(MeasIndDayConvert.INSTANCE.convertList(list));
    }

    /**
     * 量测设备指标统计日分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 MeasIndDayPageReqVO
     * @return CommonResult<PageResult<MeasIndDayRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<MeasIndDayRespVO>> page(@RequestQueryParam MeasIndDayPageReqVO pageVO) {
        PageResult<MeasIndDayDO> pageResult = measIndDayService.page(pageVO);
        return success(MeasIndDayConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出量测设备指标统计日Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 MeasIndDayExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam MeasIndDayExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<MeasIndDayDO> list = measIndDayService.getList(queryReqVO);
        // 导出 Excel
        List<MeasIndDayExcelVO> datas = MeasIndDayConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "量测设备指标统计日", "xlsx"), queryReqVO.getExportSheetName(),
                                                    MeasIndDayExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入量测设备指标统计日模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "量测设备指标统计日-导入模版.xls", "sheet1", MeasIndDayExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入量测设备指标统计日Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    // @PreAuthorize("@ss.hasPermission('irr:meas-ind-day:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<MeasIndDayExcelVO> list = ExcelUtils.read(file, MeasIndDayExcelVO.class);
        return success(measIndDayService.importExcel(list, isUpdate));
    }
}