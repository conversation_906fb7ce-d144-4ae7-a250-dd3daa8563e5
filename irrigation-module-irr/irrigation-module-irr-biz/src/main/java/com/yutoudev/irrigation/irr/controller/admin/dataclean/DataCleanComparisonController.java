package com.yutoudev.irrigation.irr.controller.admin.dataclean;

import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.coef.vo.levelflow.CoefDetailRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.coef.CoefLevelFlowDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.enums.EquipCleanCalcTypeEnum;
import com.yutoudev.irrigation.irr.service.coef.CoefLevelFlowService;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/data-clean")
public class DataCleanComparisonController {

    private static final String MODULE_NAME = "数据清洗数据对比";

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private CoefLevelFlowService<CoefLevelFlowDO> coefLevelFlowService;

    @RequestMapping("/comparison/{equipId}")
    @PreAuthorize("@ss.hasAnyPermissions('irr:equip-base:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public String getDataCleanComparison(@PathVariable("equipId") Long equipId) {

        List<CoefLevelFlowDO> levelFlowList = coefLevelFlowService.getList(List.of(equipId));
        if (CollectionUtils.isEmpty(levelFlowList)) {
            return "该设备未导入水位流量关系表";
        }

        EquipBaseDO equip = equipBaseService.get(equipId);

        if (Objects.equals(equip.getCleanCalcType(), EquipCleanCalcTypeEnum.LEVEL_FLOW.getType())) {
            return "";
        }

        return "";
    }
}
