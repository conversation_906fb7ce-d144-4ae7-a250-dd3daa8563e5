package com.yutoudev.irrigation.irr.service.iot.strategy.impl;

import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindday.MeasIndDayUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourUpdateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindtime.MeasIndTimeCreateReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndDayDO;
import com.yutoudev.irrigation.irr.enums.EquipCleanCalcTypeEnum;
import com.yutoudev.irrigation.irr.enums.EquipDeviceTypeEnum;
import com.yutoudev.irrigation.irr.service.iot.strategy.OperationStrategyEnum;
import com.yutoudev.irrigation.irr.service.waterdatacalc.WaterDataCalcService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 流量计数据清洗策略
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FlowMeterDataCleaningStrategy extends BaseDataCleaningStrategy {

    @Resource
    private WaterDataCalcService waterDataCalcService;

    @PostConstruct
    public void init() {
        setTimeCalcConsumer(this::setTimeCalcData);
        setHourCalcFunction(this::setHourCalcData);
        setDayCalcFunction(this::setDayCalcData);
    }

    @Override
    public Integer getSupportedDeviceType() {
        return EquipDeviceTypeEnum.FLOW_METER.getType();
    }

    private void setTimeCalcData(EquipBaseCacheVO equip, MeasIndTimeCreateReqVO create) {

        double flow;
        // 根据水位流量速查表获取流量
        if (Objects.equals(equip.getCleanCalcType(), EquipCleanCalcTypeEnum.LEVEL_FLOW.getType())) {
            flow = waterDataCalcService.getWaterFlowByLevel(equip, create.getWaterLevel());
            double waterSpeed = waterDataCalcService.getWaterSpeedByFlow(equip, create.getWaterLevel(), flow);
            create.setWaterSpeed((float) waterSpeed);
            create.setCalcWaterFlow((float) flow);
        }

        // 计算水量
        double waterVolume = waterDataCalcService.getWaterVolumeByFlow(create, (double) create.getWaterFlow());
        create.setCalcWaterVolume((float) waterVolume);
    }

    private Integer setHourCalcData(EquipBaseCacheVO equip, MeasIndHourUpdateReqVO update) {
        return processHourWaterData(equip, update, (MeasIndHourUpdateReqVO data) -> {
        });
    }

    private Integer setDayCalcData(LocalDateTime reportTime, MeasIndDayDO dayData, MeasIndDayUpdateReqVO update) {
        return OperationStrategyEnum.IGNORE.getType();
    }
}
