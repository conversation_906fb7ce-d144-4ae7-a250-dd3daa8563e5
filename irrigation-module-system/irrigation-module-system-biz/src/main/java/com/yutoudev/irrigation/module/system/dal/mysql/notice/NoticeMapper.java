package com.yutoudev.irrigation.module.system.dal.mysql.notice;

import cn.hutool.core.util.ObjectUtil;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.yutoudev.irrigation.module.system.dal.dataobject.notice.NoticeDO;
import com.yutoudev.irrigation.module.system.enums.dept.DeptIdEnum;
import com.yutoudev.irrigation.module.system.enums.notice.NoticeTypeEnum;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface NoticeMapper extends BaseMapperX<NoticeDO> {

    default PageResult<NoticeDO> selectPage(NoticePageReqVO reqVO) {
        LambdaQueryWrapperX<NoticeDO> queryWrapperX = new LambdaQueryWrapperX<NoticeDO>()
                .likeIfPresent(NoticeDO::getTitle, reqVO.getTitle())
                .eqIfPresent(NoticeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(NoticeDO::getType, reqVO.getType())
                .orderByDesc(NoticeDO::getId);
        //如果不是考试通知，则显示时包括顶层部门的信息
        if (ObjectUtil.isNotEmpty(reqVO.getType())
                && !reqVO.getType().equals(NoticeTypeEnum.NOTICE.getType())
                && ObjectUtil.isNotEmpty(reqVO.getDeptId())
                && !reqVO.getDeptId().equals(DeptIdEnum.DEPT_ROOT.getId())) {
            queryWrapperX.in(NoticeDO::getDeptId, reqVO.getDeptId(), DeptIdEnum.DEPT_ROOT.getId());
        } else {
            queryWrapperX.eqIfPresent(NoticeDO::getDeptId, reqVO.getDeptId());
        }
        return selectPage(reqVO, queryWrapperX);
    }

}
